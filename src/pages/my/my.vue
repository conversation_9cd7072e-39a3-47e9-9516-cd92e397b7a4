<template>
  <view class="my-page">
    <!-- 自定义导航栏 -->
    <CustomNavBar
      title="我的"
      :show-back="false"
      background-color="transparent"
      title-color="#333333"
    />

    <!-- 顶部背景区域 -->
    <view class="header-bg">
      <!-- 用户信息卡片 -->
      <UserInfoCard
        :user-info="userInfo"
        @click="handleUserInfoClick"
        class="user-info-card"
      />
    </view>

    <!-- 统计和订单合并区域 -->
    <view class="combined-section">
      <!-- 合并的认养统计卡片 -->
      <view class="adoption-card" @click="handleAdoptionClick">
        <view class="adoption-content">
          <view class="adoption-stats">
            <view class="stat-item">
              <text class="stat-title">我认养的果树</text>
              <view class="stat-value">
                <text class="stat-number">{{ adoptionStats.treeCount }}</text>
                <text class="stat-unit">棵果树</text>
              </view>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ adoptionStats.equityCount }}</text>
              <text class="stat-unit">个权益</text>
            </view>
          </view>
          <view class="tree-icon">
            <image src="/static/tree-icon.png" class="tree-image" mode="aspectFit" />
          </view>
        </view>
        <text class="order-label">我的订单</text>
      </view>

      <!-- 订单状态区域 -->
      <view class="order-grid">
        <OrderStatusItem
          v-for="(item, index) in orderStatus"
          :key="index"
          :icon="item.icon"
          :title="item.title"
          @click="handleOrderClick(index)"
          class="order-item"
        />
      </view>
    </view>

    <!-- 功能菜单区域 -->
    <view class="menu-section">
      <view class="menu-list">
        <MenuListItem
          v-for="(item, index) in menuList"
          :key="index"
          :icon="item.icon"
          :title="item.title"
          @click="handleMenuClick(index)"
          class="menu-item"
        />
      </view>

      <!-- 退出登录按钮 -->
      <view class="logout-btn" @click="handleLogout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import { getProfile } from '@/api/user'
import CustomNavBar from '@/components/CustomNavBar.vue'
import UserInfoCard from './components/UserInfoCard.vue'
import OrderStatusItem from './components/OrderStatusItem.vue'
import MenuListItem from './components/MenuListItem.vue'

// 用户store（仅用于登录状态管理）
const userStore = useUserStore()

// 用户信息 - 每次都从API获取最新数据
const userInfo = ref({})

// 认养统计数据
const adoptionStats = ref({
  treeCount: 0,
  equityCount: 0
})

// 订单状态
const orderStatus = ref([
  { icon: '/static/order-all.svg', title: '全部订单' },
  { icon: '/static/order-unpay.svg', title: '待付款' },
  { icon: '/static/order-wait-deliver.svg', title: '待配送' },
  { icon: '/static/order-wait-receive.svg', title: '待收货' }
])

// 功能菜单
const menuList = ref([
  { icon: '/static/address.svg', title: '收货地址' },
  { icon: '/static/business-cooperation.svg', title: '商务合作' },
  { icon: '/static/contact-service-2.svg', title: '联系客服' },
  { icon: '/static/adoption-benefits.svg', title: '关于我们' }
])

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const response = await getProfile()
    userInfo.value = response.data || {}

    // 更新认养统计数据
    if (userInfo.value.stats) {
      adoptionStats.value.treeCount = userInfo.value.stats.adoptedTreesCount || 0
      adoptionStats.value.equityCount = userInfo.value.stats.claimedBenefitsCount || 0
    }
  } catch (error) {
    console.error('获取用户信息失败', error)
    uni.showToast({
      title: '加载失败，请稍后重试',
      icon: 'none'
    })
  }
}

onShow(() => {
  // 每次显示页面都获取最新用户信息
  fetchUserInfo()
})

// 处理订单状态点击
const handleOrderClick = (index) => {
  const routes = [
    '/pages/my/order/order',
    '/pages/my/order/order?status=1',
    '/pages/my/order/order?status=2',
    '/pages/my/order/order?status=3'
  ]
  uni.navigateTo({
    url: routes[index]
  })
}

// 处理用户信息点击
const handleUserInfoClick = () => {
  uni.navigateTo({
    url: '/pages/my/profile/profile'
  })
}

// 处理认养卡片点击
const handleAdoptionClick = () => {
  uni.navigateTo({
    url: '/pages/my/adoption/adoption'
  })
}

// 处理菜单点击
const handleMenuClick = (index) => {
  const routes = [
    '/pages/address/list/list',
    '/pages/my/business-cooperation/business-cooperation',
    '', // 客服页面暂时留空，需要创建
    '/pages/my/about-us/about-us'
  ]

  // 如果是客服选项且页面不存在，显示提示
  if (index === 2) {
    uni.showToast({
      title: '客服功能开发中',
      icon: 'none'
    })
    return
  }

  uni.navigateTo({
    url: routes[index]
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 使用store的logout方法
        userStore.logout()
      }
    }
  })
}
</script>

<style>
page {
  background-color: #f5f5f5;
}
</style>

<style lang="scss" scoped>
// 颜色变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-dark: #1a1a1a;
$text-gray: #666666;
$text-light: #767676;
$bg-gray: #f5f5f5;
$border-gray: #e9e9e9;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin card-style {
  background-color: $white-color;
  border-radius: 20rpx;
}

.my-page {
  min-height: 100vh;
  background-color: $bg-gray;

  .header-bg {
    width: 100%;
    background: linear-gradient(180deg, #f9edd5 0%, #f6f6f6 97.86%);
    padding: 300rpx 32rpx 32rpx; // 增加顶部内边距，为导航栏和用户信息留出足够空间
    margin-top: -100rpx; // 向上偏移，让背景延伸到状态栏区域，保持沉浸式效果
    box-sizing: border-box;

    .user-info-card {
      width: 100%;
    }
  }

  .combined-section {
    margin: 0rpx 32rpx 36rpx;
    border-radius: 20rpx;
    background: linear-gradient(180deg, #F6E7C3 0%, #FBFAF3 71%);
    padding: 20rpx;

    .adoption-card {
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 20rpx;
      padding: 32rpx;
      margin-bottom: 32rpx;
      cursor: pointer;

      .adoption-content {
        @include flex-between;
        margin-bottom: 16rpx;

        .adoption-stats {
          flex: 1;

          .stat-item {
            margin-bottom: 16rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .stat-title {
              font-size: 28rpx;
              color: $text-dark;
              display: block;
              margin-bottom: 8rpx;
            }

            .stat-value {
              @include flex-center;
              justify-content: flex-start;
              gap: 8rpx;
            }

            .stat-number {
              font-size: 48rpx;
              font-weight: bold;
              color: $primary-color;
            }

            .stat-unit {
              font-size: 24rpx;
              color: $text-gray;
            }
          }
        }

        .tree-icon {
          width: 120rpx;
          height: 120rpx;
          @include flex-center;

          .tree-image {
            width: 100%;
            height: 100%;
          }
        }
      }

      .order-label {
        font-size: 24rpx;
        color: $text-gray;
        text-align: left;
      }
    }

    .order-grid {
      display: flex;
      justify-content: space-between;

      .order-item {
        flex: 1;
        @include flex-center;
        flex-direction: column;
      }
    }
  }

  .menu-section {
    margin: 0 32rpx 36rpx;

    .menu-list {
      @include card-style;
      overflow: hidden;
      margin-bottom: 80rpx;

      .menu-item {
        border-bottom: 1rpx solid $border-gray;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .logout-btn {
      @include card-style;
      height: 88rpx;
      @include flex-center;
      border-radius: 326rpx;
      margin-top: 80rpx;

      .logout-text {
        font-size: 32rpx;
        color: $primary-color;
      }
    }
  }
}
</style>